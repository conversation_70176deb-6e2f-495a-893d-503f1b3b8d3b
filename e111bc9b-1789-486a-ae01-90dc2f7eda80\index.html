<!DOCTYPE html>
<html>

  <head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="renderer" content="webkit">
    <meta name="format-detection" content="telephone=no">
    <meta name="format-detection" content="email=no">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" />
    <link rel="dns-prefetch" href="//g.alicdn.com" />
    <title>Lab 低代码平台页面渲染</title>
    <link rel="stylesheet" href="//g.alicdn.com/platform/common/s/1.1/global/global.css" />
    <link rel="stylesheet" href="./web.css">
    <style type="text/css">
      body {
        -webkit-overflow-scrolling: touch;
      }
    </style>
  </head>

  <body>
    <div id="App"></div>
    <script>
      window.pageConfig = {
        device: 'web'
      }
      window.g_config = {
        "id": "App",
        "isSinglePage": true,
        "historyType": "HASH",
        "locale": "zh_CN",
        "navConfig": {
          // 当前导航的覆盖配置，请勿修改，否则会导致渲染异常
          "showNav": true,
          "showTodoTasks": false,
          "showPersonInfo": false,
          "showMsg": false,
          "navLogo": "https://tianshu.alicdn.com/052a190e-c961-4afe-aa4c-49ee9722952d.svg",
        },
      };
    </script>
    <script crossorigin="anonymous" src="//polyfill.alicdn.com/s/polyfill.min.js?features=default,es2017,es6,fetch,RegeneratorRuntime"></script>

    <script crossorigin="anonymous" src="//g.alicdn.com/code/lib/moment.js/2.24.0/moment-with-locales.min.js"></script>
    <script crossorigin="anonymous" src="//g.alicdn.com/code/lib/??react/16.13.0/umd/react.production.min.js,react-dom/16.13.0/umd/react-dom.production.min.js,prop-types/15.7.2/prop-types.js,react-router/5.1.2/react-router.min.js,react-router-dom/5.1.2/react-router-dom.min.js"></script>
    <script crossorigin="anonymous" src="//g.alicdn.com/platform/c/??react15-polyfill/0.0.1/dist/index.js,lie/3.0.2/dist/lie.polyfill.min.js,lodash/4.6.1/lodash.min.js,immutable/3.7.6/dist/immutable.min.js,natty-storage/2.0.2/dist/natty-storage.min.js,natty-fetch/2.6.0/dist/natty-fetch.pc.min.js,tinymce/4.2.5/tinymce-full.js"></script>

    <script crossorigin="anonymous" src="//g.alicdn.com/vision/render-engine/7.5.23/render-engine.min.js"></script>
    <script crossorigin="anonymous" src="./web.js"></script>

  </body>

</html>